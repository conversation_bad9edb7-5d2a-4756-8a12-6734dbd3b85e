2025-08-06 20:22:22,783 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 20:22:24,804 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 20:22:24,805 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 20:22:26,837 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 20:22:26,838 - auto_processor - INFO - File watcher setup for: C:\AI-Hub\Applications\Clipper_Neon\data_in
2025-08-06 20:22:26,838 - clipper_main - INFO - Starting processing for: https://youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:22:26,838 - clipper_main - INFO - Step 1: Downloading video...
2025-08-06 20:22:26,838 - video_downloader - INFO - Starting download for: https://youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:22:26,838 - video_downloader - INFO - Fetching video info for: https://youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:22:30,852 - video_downloader - INFO - Video info retrieved: we broke up.. (713s)
2025-08-06 20:22:30,853 - video_downloader - INFO - Executing yt-dlp download...
2025-08-06 20:22:37,682 - video_downloader - INFO - Download completed: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 20:22:37,682 - clipper_main - INFO - Step 2: Generating transcript...
2025-08-06 20:22:38,052 - transcription_engine - INFO - Accessing YouTube transcript data: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:22:43,985 - transcription_engine - WARNING - No transcript data available for this video
2025-08-06 20:22:43,994 - clipper_main - INFO - Step 3: Detecting highlights with AI...
2025-08-06 20:22:43,994 - highlight_detector - INFO - Detecting highlights using deepseek-r1:latest
2025-08-06 20:22:57,402 - highlight_detector - WARNING - Skipping invalid highlight data: float() argument must be a string or a real number, not 'NoneType'
2025-08-06 20:22:57,402 - highlight_detector - INFO - Parsed 0 highlights from AI response
2025-08-06 20:22:57,402 - highlight_detector - INFO - Raw highlights before filtering: 0
2025-08-06 20:22:57,402 - highlight_detector - INFO - Detected 0 valid highlights
2025-08-06 20:22:57,402 - clipper_main - INFO - Step 4: Extracting video clips...
2025-08-06 20:22:57,402 - clipper_main - INFO - Step 5: Organizing clips...
2025-08-06 20:22:57,403 - clipper_main - ERROR - Error processing video https://youtube.com/watch?v=xKa_6kzOl0M: [Errno 2] No such file or directory: 'C:\\AI-Hub\\Applications\\Clipper_Neon\\data_out\\1. we broke up..\\clips_info.json'
