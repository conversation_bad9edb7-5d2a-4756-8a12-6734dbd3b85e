"""
Direct YouTube Transcript Engine for Clipper_Neon
=================================================

Tesla Assembly Line Approach: Automate the exact manual process
- Go to YouTube video
- Access transcript data directly from DOM
- Get perfect timestamps from YouTube itself
- Zero APIs, Zero costs, Perfect accuracy
"""

import os
import time
import logging
from typing import Dict, List, Optional
from urllib.parse import urlparse, parse_qs


class TranscriptionEngine:
    """
    Direct YouTube Transcript Engine

    Mimics exactly what you do manually:
    1. Go to YouTube video
    2. Access transcript data directly from DOM
    3. Get perfect timestamps from YouTube itself
    """

    def __init__(self, config):
        """Initialize the direct transcript engine."""
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Check for browser automation
        self.playwright_available = self._check_playwright()
        if self.playwright_available:
            self.logger.info("✅ Direct YouTube transcript extraction ready")
        else:
            self.logger.warning("❌ Install playwright for direct transcript access: pip install playwright && playwright install chromium")

    def _check_playwright(self) -> bool:
        """Check if <PERSON><PERSON> is available for direct YouTube access."""
        try:
            from playwright.sync_api import sync_playwright
            return True
        except ImportError:
            return False

    def transcribe(self, video_path: str, video_id: str = None) -> List[Dict]:
        """Get transcript directly from YouTube - the manual way, automated"""

        if not video_id:
            self.logger.error("Video ID required for direct YouTube extraction")
            return self._fallback()

        video_url = f"https://www.youtube.com/watch?v={video_id}"

        if self.playwright_available:
            transcript = self._extract_direct_from_youtube(video_url)
            if transcript:
                return transcript

        return self._fallback()

    def _extract_direct_from_youtube(self, video_url: str) -> Optional[List[Dict]]:
        """The magic method: Get transcript exactly like manual process"""
        try:
            from playwright.sync_api import sync_playwright

            with sync_playwright() as p:
                browser = p.chromium.launch(headless=True)
                page = browser.new_page()

                self.logger.info(f"Accessing YouTube transcript data: {video_url}")
                page.goto(video_url, wait_until='domcontentloaded')
                time.sleep(3)

                # Access YouTube's internal transcript data (same data as manual transcript)
                transcript_js = """
                (async () => {
                    try {
                        // Access the exact same data YouTube uses for "Show transcript"
                        if (window.ytInitialPlayerResponse?.captions?.playerCaptionsTracklistRenderer?.captionTracks) {
                            const tracks = window.ytInitialPlayerResponse.captions.playerCaptionsTracklistRenderer.captionTracks;

                            // Get English track or first available (same priority as manual selection)
                            let selectedTrack = tracks.find(track => track.languageCode === 'en') || tracks[0];

                            if (selectedTrack) {
                                // Fetch from YouTube's own API endpoint (same as transcript panel)
                                const response = await fetch(selectedTrack.baseUrl + '&fmt=json3');
                                const data = await response.json();

                                const transcript = [];

                                for (const event of data.events || []) {
                                    if (event.segs) {
                                        const text = event.segs
                                            .map(seg => seg.utf8 || '')
                                            .join('')
                                            .replace(/\\n/g, ' ')
                                            .trim();

                                        if (text) {
                                            const startTime = (event.tStartMs || 0) / 1000;
                                            const duration = (event.dDurationMs || 3000) / 1000;

                                            transcript.push({
                                                text: text,
                                                start: Math.round(startTime * 100) / 100,
                                                end: Math.round((startTime + duration) * 100) / 100,
                                                duration: Math.round(duration * 100) / 100,
                                                confidence: 1.0  // YouTube transcripts are highly accurate
                                            });
                                        }
                                    }
                                }

                                return transcript.length > 0 ? transcript : null;
                            }
                        }
                        return null;
                    } catch (error) {
                        return null;
                    }
                })()
                """

                result = page.evaluate(transcript_js)
                browser.close()

                if result and len(result) > 0:
                    self.logger.info(f"✅ SUCCESS: Got {len(result)} transcript segments with PERFECT timestamps!")
                    return result

                self.logger.warning("No transcript data available for this video")
                return None

        except Exception as e:
            self.logger.error(f"Direct transcript extraction failed: {e}")
            return None

    def _fallback(self) -> List[Dict]:
        """Fallback when transcript not available"""
        return [{
            'text': "Direct YouTube transcript not available. Install playwright for transcript access.",
            'start': 0.0,
            'end': 10.0,
            'duration': 10.0,
            'confidence': 0.1
        }]

    def _extract_video_id(self, youtube_url: str) -> str:
        """Extract video ID from YouTube URL."""
        parsed_url = urlparse(youtube_url)
        if parsed_url.hostname == 'youtu.be':
            return parsed_url.path[1:]
        elif parsed_url.hostname in ('www.youtube.com', 'youtube.com'):
            if parsed_url.path == '/watch':
                return parse_qs(parsed_url.query)['v'][0]
            elif parsed_url.path[:7] == '/embed/':
                return parsed_url.path.split('/')[2]
            elif parsed_url.path[:3] == '/v/':
                return parsed_url.path.split('/')[2]
        return ""


if __name__ == "__main__":
    # Test the direct YouTube transcript engine
    from config import ClipperConfig

    config = ClipperConfig()
    transcriber = TranscriptionEngine(config)

    print("🎬 Direct YouTube Transcript Engine")
    print(f"✅ Playwright available: {transcriber.playwright_available}")
    print("🚀 Ready for Tesla assembly line transcript extraction!")

    # Test with a video ID
    # transcript = transcriber.transcribe("", video_id="xKa_6kzOl0M")
    # print(f"Transcript segments: {len(transcript)}")
            if hasattr(self, 'enhanced_whisper') and self.enhanced_whisper:
                # Use enhanced whisper-timestamped for better accuracy
                import whisper_timestamped as whisper

                self.logger.info("Loading enhanced Whisper model...")
                model_size = getattr(self.config, 'whisper_model_size', 'base')
                model = whisper.load_model(model_size, device="cpu")

                self.logger.info("Transcribing with enhanced Whisper (accurate timestamps)...")

                # Enhanced transcription with word-level timestamps
                result = whisper.transcribe(
                    model,
                    audio_path,
                    language=None if self.config.language == 'auto' else self.config.language,
                    vad="silero",  # Voice Activity Detection for better segmentation
                    verbose=False
                )

                # Extract text with precise timestamps
                transcript_parts = []
                for segment in result['segments']:
                    start_time = segment['start']
                    text = segment['text'].strip()

                    # Format: [MM:SS] Text
                    timestamp = f"[{int(start_time//60):02d}:{int(start_time%60):02d}]"
                    transcript_parts.append(f"{timestamp} {text}")

                transcript = '\n'.join(transcript_parts)
                self.logger.info(f"Enhanced Whisper transcription completed. Language: {result.get('language', 'unknown')}")

            else:
                # Fallback to standard Whisper
                import whisper

                self.logger.info("Loading standard Whisper model...")
                model_size = getattr(self.config, 'whisper_model_size', 'base')
                model = whisper.load_model(model_size)

                self.logger.info("Transcribing with standard Whisper...")

                # Standard transcription
                result = model.transcribe(
                    audio_path,
                    language=None if self.config.language == 'auto' else self.config.language,
                    verbose=False
                )

                # Extract text with timestamps
                transcript_parts = []
                for segment in result['segments']:
                    start_time = segment['start']
                    text = segment['text'].strip()

                    # Format: [MM:SS] Text
                    timestamp = f"[{int(start_time//60):02d}:{int(start_time%60):02d}]"
                    transcript_parts.append(f"{timestamp} {text}")

                transcript = '\n'.join(transcript_parts)
                self.logger.info(f"Standard Whisper transcription completed. Language: {result.get('language', 'unknown')}")

            return transcript

        except Exception as e:
            self.logger.error(f"Whisper transcription failed: {e}")
            return self._transcribe_fallback(audio_path)
    
    def _transcribe_fallback(self, audio_path: str) -> str:
        """Fallback transcription method when Whisper is not available."""
        self.logger.warning("Using fallback transcription method")
        
        # Try using speech_recognition library if available
        try:
            import speech_recognition as sr
            
            recognizer = sr.Recognizer()
            
            with sr.AudioFile(audio_path) as source:
                audio_data = recognizer.record(source)
            
            # Try Google Speech Recognition (requires internet)
            try:
                transcript = recognizer.recognize_google(audio_data)
                self.logger.info("Transcription completed using Google Speech Recognition")
                return transcript
            except sr.UnknownValueError:
                self.logger.warning("Speech recognition could not understand audio")
            except sr.RequestError as e:
                self.logger.warning(f"Speech recognition service error: {e}")
            
        except ImportError:
            self.logger.warning("speech_recognition library not available")
        
        # Ultimate fallback - return placeholder
        self.logger.warning("No transcription method available. Using placeholder.")
        return "[Transcription not available - please install Whisper or speech_recognition]"
    
    def get_transcript_with_timestamps(self, video_path: str) -> Dict[str, Any]:
        """
        Get detailed transcript with precise timestamps.
        
        Returns:
            Dictionary with transcript data including word-level timestamps
        """
        if not self.whisper_available:
            self.logger.warning("Detailed timestamps require Whisper")
            return {
                'text': self.transcribe(video_path),
                'segments': [],
                'words': []
            }
        
        audio_path = self._extract_audio(video_path)
        
        try:
            import whisper
            
            model = whisper.load_model(getattr(self.config, 'whisper_model_size', 'base'))
            
            result = model.transcribe(
                audio_path,
                language=None if self.config.language == 'auto' else self.config.language,
                word_timestamps=True,
                verbose=False
            )
            
            return {
                'text': result['text'],
                'language': result['language'],
                'segments': result['segments'],
                'words': [word for segment in result['segments'] for word in segment.get('words', [])]
            }
            
        finally:
            if Path(audio_path).exists():
                Path(audio_path).unlink()
    
    def save_transcript(self, transcript: str, output_path: str):
        """Save transcript to file."""
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(transcript)
        
        self.logger.info(f"Transcript saved to: {output_path}")


if __name__ == "__main__":
    # Test the transcription engine
    from config import ClipperConfig
    
    config = ClipperConfig()
    transcriber = TranscriptionEngine(config)
    
    print(f"Transcription engine initialized")
    print(f"Whisper available: {transcriber.whisper_available}")
    print(f"FFmpeg path: {config.ffmpeg_path}")
    
    # Test audio extraction (would need actual video file)
    # transcript = transcriber.transcribe("test_video.mp4")
