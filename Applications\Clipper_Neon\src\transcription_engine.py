"""
Transcription Engine for Clipper_Neon
=====================================

Handles transcription using YouTube's native transcripts (preferred)
or Whisper fallback for perfect timestamp accuracy.
"""

import os
import subprocess
import logging
import tempfile
import re
from pathlib import Path
from typing import Optional, Dict, Any, List
from urllib.parse import urlparse, parse_qs


class TranscriptionEngine:
    """Handles audio extraction and transcription."""
    
    def __init__(self, config):
        """Initialize the transcription engine."""
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Check available transcription methods
        self._check_youtube_transcript_api()
        self._check_whisper_availability()
    
    def _check_youtube_transcript_api(self):
        """Check if YouTube Transcript API is available."""
        try:
            from youtube_transcript_api import YouTubeTranscriptApi
            self.youtube_transcript_available = True
            self.logger.info("YouTube Transcript API available for perfect timestamps")
        except ImportError:
            self.youtube_transcript_available = False
            self.logger.warning("YouTube Transcript API not available. Install with: pip install youtube-transcript-api")

    def _check_whisper_availability(self):
        """Check if enhanced Whisper is available for transcription."""
        try:
            import whisper_timestamped as whisper
            self.whisper_available = True
            self.enhanced_whisper = True
            self.logger.info("Enhanced whisper-timestamped available for accurate timestamps")
        except ImportError:
            try:
                import whisper
                self.whisper_available = True
                self.enhanced_whisper = False
                self.logger.info("Standard Whisper available (consider upgrading to whisper-timestamped)")
            except (ImportError, Exception) as e:
                self.whisper_available = False
                self.enhanced_whisper = False
                if "NumPy" in str(e):
                    self.logger.warning("Whisper unavailable due to NumPy compatibility issue. Using YouTube transcripts only.")
                else:
                    self.logger.warning("Whisper not available. Install with: pip install whisper-timestamped")
    
    def transcribe(self, video_path: str, youtube_url: Optional[str] = None) -> str:
        """
        Transcribe video to text using best available method.

        Args:
            video_path: Path to video file
            youtube_url: Optional YouTube URL for native transcript access

        Returns:
            Transcript text with timestamps
        """
        self.logger.info(f"Starting transcription for: {video_path}")

        # Try YouTube native transcript first (most accurate timestamps)
        if youtube_url and self.youtube_transcript_available:
            try:
                transcript = self._transcribe_from_youtube(youtube_url)
                if transcript:
                    self.logger.info(f"YouTube transcript completed. Length: {len(transcript)} characters")
                    return transcript
            except Exception as e:
                self.logger.warning(f"YouTube transcript failed, falling back to Whisper: {e}")

        # Fallback to Whisper transcription
        audio_path = self._extract_audio(video_path)

        try:
            # Transcribe audio to text
            if self.whisper_available:
                transcript = self._transcribe_with_whisper(audio_path)
            else:
                transcript = self._transcribe_fallback(audio_path)

            self.logger.info(f"Transcription completed. Length: {len(transcript)} characters")
            return transcript

        finally:
            # Clean up temporary audio file
            if Path(audio_path).exists():
                Path(audio_path).unlink()
                self.logger.debug(f"Cleaned up audio file: {audio_path}")

    def _transcribe_from_youtube(self, youtube_url: str) -> Optional[str]:
        """
        Get transcript directly from YouTube with perfect timestamps.

        Args:
            youtube_url: YouTube video URL

        Returns:
            Formatted transcript with timestamps or None if failed
        """
        try:
            from youtube_transcript_api import YouTubeTranscriptApi
            from youtube_transcript_api._errors import TranscriptsDisabled, NoTranscriptFound

            # Extract video ID from URL
            video_id = self._extract_video_id(youtube_url)

            self.logger.info(f"Fetching YouTube transcript for video ID: {video_id}")

            # Get transcript using correct API method
            transcript_list = YouTubeTranscriptApi.get_transcript(video_id, languages=['en'])

            # Format transcript with timestamps
            formatted_transcript = []
            for entry in transcript_list:
                start_time = entry['start']
                text = entry['text'].strip()

                # Format: [MM:SS] Text
                minutes = int(start_time // 60)
                seconds = int(start_time % 60)
                timestamp = f"[{minutes:02d}:{seconds:02d}]"

                formatted_transcript.append(f"{timestamp} {text}")

            transcript = '\n'.join(formatted_transcript)

            self.logger.info(f"YouTube transcript retrieved: {len(transcript_list)} segments")
            return transcript

        except (TranscriptsDisabled, NoTranscriptFound) as e:
            self.logger.warning(f"YouTube transcript not available: {e}")
            return None
        except Exception as e:
            self.logger.warning(f"YouTube transcript extraction failed: {e}")
            return None

    def _extract_video_id(self, youtube_url: str) -> str:
        """Extract video ID from YouTube URL."""
        parsed_url = urlparse(youtube_url)

        if parsed_url.hostname in ['youtu.be']:
            return parsed_url.path[1:]
        elif parsed_url.hostname in ['www.youtube.com', 'youtube.com']:
            if parsed_url.path == '/watch':
                return parse_qs(parsed_url.query)['v'][0]
            elif parsed_url.path.startswith('/embed/'):
                return parsed_url.path.split('/')[2]
            elif parsed_url.path.startswith('/v/'):
                return parsed_url.path.split('/')[2]

        raise ValueError(f"Could not extract video ID from URL: {youtube_url}")

    def _extract_audio(self, video_path: str) -> str:
        """Extract audio from video using ffmpeg."""
        self.logger.info("Extracting audio from video...")
        
        # Create temporary audio file
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_audio:
            audio_path = temp_audio.name
        
        # Build ffmpeg command for audio extraction
        cmd = [
            self.config.ffmpeg_path,
            "-i", video_path,
            "-vn",  # No video
            "-acodec", "pcm_s16le",  # PCM 16-bit little-endian
            "-ar", "16000",  # 16kHz sample rate (good for speech)
            "-ac", "1",  # Mono
            "-y",  # Overwrite output
            audio_path
        ]
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True
            )
            
            if not Path(audio_path).exists():
                raise FileNotFoundError("Audio extraction failed - no output file")
            
            file_size = Path(audio_path).stat().st_size
            self.logger.info(f"Audio extracted: {audio_path} ({file_size / 1024 / 1024:.1f} MB)")
            
            return audio_path
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Audio extraction failed: {e.stderr}")
            raise
    
    def _transcribe_with_whisper(self, audio_path: str) -> str:
        """Transcribe audio using enhanced Whisper with accurate timestamps."""
        try:
            if hasattr(self, 'enhanced_whisper') and self.enhanced_whisper:
                # Use enhanced whisper-timestamped for better accuracy
                import whisper_timestamped as whisper

                self.logger.info("Loading enhanced Whisper model...")
                model_size = getattr(self.config, 'whisper_model_size', 'base')
                model = whisper.load_model(model_size, device="cpu")

                self.logger.info("Transcribing with enhanced Whisper (accurate timestamps)...")

                # Enhanced transcription with word-level timestamps
                result = whisper.transcribe(
                    model,
                    audio_path,
                    language=None if self.config.language == 'auto' else self.config.language,
                    vad="silero",  # Voice Activity Detection for better segmentation
                    verbose=False
                )

                # Extract text with precise timestamps
                transcript_parts = []
                for segment in result['segments']:
                    start_time = segment['start']
                    text = segment['text'].strip()

                    # Format: [MM:SS] Text
                    timestamp = f"[{int(start_time//60):02d}:{int(start_time%60):02d}]"
                    transcript_parts.append(f"{timestamp} {text}")

                transcript = '\n'.join(transcript_parts)
                self.logger.info(f"Enhanced Whisper transcription completed. Language: {result.get('language', 'unknown')}")

            else:
                # Fallback to standard Whisper
                import whisper

                self.logger.info("Loading standard Whisper model...")
                model_size = getattr(self.config, 'whisper_model_size', 'base')
                model = whisper.load_model(model_size)

                self.logger.info("Transcribing with standard Whisper...")

                # Standard transcription
                result = model.transcribe(
                    audio_path,
                    language=None if self.config.language == 'auto' else self.config.language,
                    verbose=False
                )

                # Extract text with timestamps
                transcript_parts = []
                for segment in result['segments']:
                    start_time = segment['start']
                    text = segment['text'].strip()

                    # Format: [MM:SS] Text
                    timestamp = f"[{int(start_time//60):02d}:{int(start_time%60):02d}]"
                    transcript_parts.append(f"{timestamp} {text}")

                transcript = '\n'.join(transcript_parts)
                self.logger.info(f"Standard Whisper transcription completed. Language: {result.get('language', 'unknown')}")

            return transcript

        except Exception as e:
            self.logger.error(f"Whisper transcription failed: {e}")
            return self._transcribe_fallback(audio_path)
    
    def _transcribe_fallback(self, audio_path: str) -> str:
        """Fallback transcription method when Whisper is not available."""
        self.logger.warning("Using fallback transcription method")
        
        # Try using speech_recognition library if available
        try:
            import speech_recognition as sr
            
            recognizer = sr.Recognizer()
            
            with sr.AudioFile(audio_path) as source:
                audio_data = recognizer.record(source)
            
            # Try Google Speech Recognition (requires internet)
            try:
                transcript = recognizer.recognize_google(audio_data)
                self.logger.info("Transcription completed using Google Speech Recognition")
                return transcript
            except sr.UnknownValueError:
                self.logger.warning("Speech recognition could not understand audio")
            except sr.RequestError as e:
                self.logger.warning(f"Speech recognition service error: {e}")
            
        except ImportError:
            self.logger.warning("speech_recognition library not available")
        
        # Ultimate fallback - return placeholder
        self.logger.warning("No transcription method available. Using placeholder.")
        return "[Transcription not available - please install Whisper or speech_recognition]"
    
    def get_transcript_with_timestamps(self, video_path: str) -> Dict[str, Any]:
        """
        Get detailed transcript with precise timestamps.
        
        Returns:
            Dictionary with transcript data including word-level timestamps
        """
        if not self.whisper_available:
            self.logger.warning("Detailed timestamps require Whisper")
            return {
                'text': self.transcribe(video_path),
                'segments': [],
                'words': []
            }
        
        audio_path = self._extract_audio(video_path)
        
        try:
            import whisper
            
            model = whisper.load_model(getattr(self.config, 'whisper_model_size', 'base'))
            
            result = model.transcribe(
                audio_path,
                language=None if self.config.language == 'auto' else self.config.language,
                word_timestamps=True,
                verbose=False
            )
            
            return {
                'text': result['text'],
                'language': result['language'],
                'segments': result['segments'],
                'words': [word for segment in result['segments'] for word in segment.get('words', [])]
            }
            
        finally:
            if Path(audio_path).exists():
                Path(audio_path).unlink()
    
    def save_transcript(self, transcript: str, output_path: str):
        """Save transcript to file."""
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(transcript)
        
        self.logger.info(f"Transcript saved to: {output_path}")


if __name__ == "__main__":
    # Test the transcription engine
    from config import ClipperConfig
    
    config = ClipperConfig()
    transcriber = TranscriptionEngine(config)
    
    print(f"Transcription engine initialized")
    print(f"Whisper available: {transcriber.whisper_available}")
    print(f"FFmpeg path: {config.ffmpeg_path}")
    
    # Test audio extraction (would need actual video file)
    # transcript = transcriber.transcribe("test_video.mp4")
