2025-08-06 19:23:04,951 - transcription_engine - INFO - YouTube Transcript API available for perfect timestamps
2025-08-06 19:23:09,851 - transcription_engine - INFO - Enhanced whisper-timestamped available for accurate timestamps
2025-08-06 19:23:11,871 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 19:23:11,872 - transcription_engine - INFO - YouTube Transcript API available for perfect timestamps
2025-08-06 19:23:11,872 - transcription_engine - INFO - Enhanced whisper-timestamped available for accurate timestamps
2025-08-06 19:23:13,896 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 19:23:13,897 - auto_processor - INFO - File watcher setup for: C:\AI-Hub\Applications\Clipper_Neon\data_in
2025-08-06 19:23:13,897 - clipper_main - INFO - Starting processing for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 19:23:13,897 - clipper_main - INFO - Step 1: Downloading video...
2025-08-06 19:23:13,897 - video_downloader - INFO - Starting download for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 19:23:13,897 - video_downloader - INFO - Fetching video info for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 19:23:19,015 - video_downloader - INFO - Video info retrieved: we broke up.. (713s)
2025-08-06 19:23:19,015 - video_downloader - INFO - Executing yt-dlp download...
2025-08-06 19:23:25,459 - video_downloader - INFO - Download completed: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 19:23:25,459 - clipper_main - INFO - Step 2: Generating transcript...
2025-08-06 19:23:25,459 - transcription_engine - INFO - Starting transcription for: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 19:23:25,459 - transcription_engine - INFO - Fetching YouTube transcript for video ID: xKa_6kzOl0M
2025-08-06 19:23:25,459 - transcription_engine - WARNING - YouTube transcript extraction failed: type object 'YouTubeTranscriptApi' has no attribute 'get_transcript'
2025-08-06 19:23:25,459 - transcription_engine - INFO - Extracting audio from video...
2025-08-06 19:23:25,853 - transcription_engine - INFO - Audio extracted: C:\Users\<USER>\AppData\Local\Temp\tmp6fywj9g1.wav (21.7 MB)
2025-08-06 19:23:25,854 - transcription_engine - INFO - Loading enhanced Whisper model...
2025-08-06 19:23:26,305 - transcription_engine - INFO - Transcribing with enhanced Whisper (accurate timestamps)...
2025-08-06 19:23:26,305 - transcription_engine - ERROR - Whisper transcription failed: transcribe_timestamped() got an unexpected keyword argument 'word_timestamps'
2025-08-06 19:23:26,305 - transcription_engine - WARNING - Using fallback transcription method
2025-08-06 19:23:26,305 - transcription_engine - WARNING - speech_recognition library not available
2025-08-06 19:23:26,305 - transcription_engine - WARNING - No transcription method available. Using placeholder.
2025-08-06 19:23:26,305 - transcription_engine - INFO - Transcription completed. Length: 76 characters
2025-08-06 19:23:26,307 - clipper_main - INFO - Step 3: Detecting highlights with AI...
2025-08-06 19:23:26,307 - highlight_detector - INFO - Detecting highlights using deepseek-r1:latest
2025-08-06 19:23:53,137 - highlight_detector - INFO - Parsed 2 highlights from AI response
2025-08-06 19:23:53,137 - highlight_detector - INFO - Raw highlights before filtering: 2
2025-08-06 19:23:53,141 - highlight_detector - INFO - Detected 2 valid highlights
2025-08-06 19:23:53,141 - clipper_main - INFO - Step 4: Extracting video clips...
2025-08-06 19:23:53,141 - video_clipper - INFO - Extracting clip 1: The Shocking Breakup Reason (45.2s-78.5s)
2025-08-06 19:23:59,337 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\clip_01_The_Shocking_Breakup_Reason.mp4 (6.1 MB)
2025-08-06 19:23:59,337 - video_clipper - INFO - Extracting clip 2: The Emotional Rollercoaster (12.3s-45.6s)
2025-08-06 19:24:05,752 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\clip_02_The_Emotional_Rollercoaster.mp4 (7.2 MB)
2025-08-06 19:24:05,752 - clipper_main - INFO - Step 5: Post-processing clips...
2025-08-06 19:24:05,752 - post_processor - INFO - Post-processing 2 clips
2025-08-06 19:24:05,752 - clipper_main - ERROR - Error processing video https://www.youtube.com/watch?v=xKa_6kzOl0M: [WinError 3] The system cannot find the path specified: 'C:\\AI-Hub\\Applications\\Clipper_Neon\\data_out\\20250806_192405_we_broke_up..\\clips'
