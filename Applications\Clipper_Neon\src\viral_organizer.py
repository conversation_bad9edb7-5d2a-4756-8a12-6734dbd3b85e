"""
Viral Organization System for Clipper_Neon
==========================================

Advanced organization system with status-based management and viral metadata tracking.
"""

import os
import json
import shutil
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict

from highlight_detector import Highlight


@dataclass
class ClipMetadata:
    """Enhanced metadata for viral clips."""
    clip_number: int
    filename: str
    file_path: str
    file_size_mb: float
    status: str  # PENDING, APPROVED, REJECTED, UPLOADED
    title: str
    description: str
    duration: float
    viral_score: float
    viral_components: Dict[str, str]
    viral_scores: Dict[str, float]
    viral_reasoning: str
    suggested_platforms: List[str]
    keywords: List[str]
    created_at: str
    source_video: str
    source_url: str


class ViralOrganizer:
    """Advanced organization system for viral content creation."""
    
    def __init__(self, config):
        """Initialize the simple viral organizer."""
        self.config = config
        self.base_output_dir = Path(config.output_dir)

        # Ensure output directory exists
        self.base_output_dir.mkdir(parents=True, exist_ok=True)
    

    
    def organize_clips(self, clips: List[str], highlights: List[Highlight],
                      source_video: str, source_url: str) -> Dict[str, Any]:
        """
        Organize clips using proper YT Cutter framework structure.
        Each video gets its own numbered folder.

        Returns:
            Dict with clip info
        """
        # Find next available video folder number
        video_folder_number = self._get_next_video_folder_number()

        # Create video-specific folder: "1. Video Title/"
        clean_title = self._sanitize_filename(source_video)
        video_folder_name = f"{video_folder_number}. {clean_title}"
        video_folder = self.base_output_dir / video_folder_name
        video_folder.mkdir(exist_ok=True)

        clip_metadata_list = []

        for i, (clip_path, highlight) in enumerate(zip(clips, highlights), 1):
            if not Path(clip_path).exists():
                continue

            # Proper YT Cutter naming: "Clip 1 - Descriptive Name.mp4"
            clean_clip_title = self._sanitize_filename(highlight.title)
            new_filename = f"Clip {i} - {clean_clip_title}.mp4"

            # Move clip to video folder
            new_clip_path = video_folder / new_filename
            shutil.move(clip_path, new_clip_path)

            # Create metadata
            file_size_mb = round(new_clip_path.stat().st_size / 1024 / 1024, 2)

            clip_metadata = {
                "clip_number": i,
                "filename": new_filename,
                "file_path": str(new_clip_path),
                "file_size_mb": file_size_mb,
                "title": highlight.title,
                "viral_score": highlight.viral_score,
                "viral_reasoning": highlight.viral_reasoning,
                "suggested_platforms": highlight.suggested_platforms,
                "duration": highlight.duration
            }

            clip_metadata_list.append(clip_metadata)

        # Create clips info in video folder
        clips_info_file = video_folder / "clips_info.json"
        clips_summary = {
            "video_folder": video_folder_name,
            "source_video": source_video,
            "source_url": source_url,
            "created_at": datetime.now().isoformat(),
            "total_clips": len(clip_metadata_list),
            "clips": clip_metadata_list
        }

        with open(clips_info_file, 'w', encoding='utf-8') as f:
            json.dump(clips_summary, f, indent=2)

        print(f"📁 Clips organized in: {video_folder_name}")
        print(f"📋 Clip info: {clips_info_file}")

        return clips_summary

    def _get_next_video_folder_number(self) -> int:
        """Find the next available video folder number."""
        existing_folders = []

        for folder in self.base_output_dir.iterdir():
            if folder.is_dir() and folder.name[0].isdigit():
                try:
                    # Extract number from "1. Video Title" format
                    number = int(folder.name.split('.')[0])
                    existing_folders.append(number)
                except (ValueError, IndexError):
                    continue

        return max(existing_folders, default=0) + 1
    
    def _generate_viral_filename(self, clip_number: int, highlight: Highlight) -> str:
        """Generate viral-optimized filename with status prefix."""
        # Sanitize title for filename
        safe_title = self._sanitize_filename(highlight.title)
        viral_score = int(highlight.viral_score)
        
        return f"[PENDING] Clip {clip_number:02d} - {safe_title} (VS{viral_score}).mp4"
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for filesystem compatibility."""
        # Remove or replace invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Limit length and clean up
        filename = filename[:50].strip()
        return filename
    
    def _calculate_session_analytics(self, clips: List[ClipMetadata]) -> Dict[str, Any]:
        """Calculate viral analytics for the session."""
        if not clips:
            return {}
        
        viral_scores = [clip.viral_score for clip in clips]
        
        return {
            "total_clips": len(clips),
            "average_viral_score": round(sum(viral_scores) / len(viral_scores), 2),
            "highest_viral_score": max(viral_scores),
            "lowest_viral_score": min(viral_scores),
            "total_duration": round(sum(clip.duration for clip in clips), 2),
            "average_duration": round(sum(clip.duration for clip in clips) / len(clips), 2),
            "platform_distribution": self._get_platform_distribution(clips),
            "top_viral_components": self._get_top_viral_components(clips)
        }
    
    def _get_platform_distribution(self, clips: List[ClipMetadata]) -> Dict[str, int]:
        """Get distribution of suggested platforms."""
        platform_counts = {}
        for clip in clips:
            for platform in clip.suggested_platforms:
                platform_counts[platform] = platform_counts.get(platform, 0) + 1
        return platform_counts
    
    def _get_top_viral_components(self, clips: List[ClipMetadata]) -> List[str]:
        """Get most common viral components."""
        component_counts = {}
        for clip in clips:
            for component in clip.viral_components.keys():
                component_counts[component] = component_counts.get(component, 0) + 1
        
        # Return top 5 components
        sorted_components = sorted(component_counts.items(), key=lambda x: x[1], reverse=True)
        return [comp[0] for comp in sorted_components[:5]]
    
    def _create_clips_summary(self, clips: List[ClipMetadata], summary_file: Path):
        """Create a user-friendly clips summary for review."""
        summary = {
            "review_session": {
                "created_at": datetime.now().isoformat(),
                "total_clips": len(clips),
                "instructions": "Review clips in the clips/ folder. Delete unwanted clips, then run approval process."
            },
            "clips": []
        }
        
        for clip in clips:
            clip_summary = {
                "clip_number": clip.clip_number,
                "filename": clip.filename,
                "title": clip.title,
                "viral_score": clip.viral_score,
                "duration_seconds": clip.duration,
                "file_size_mb": clip.file_size_mb,
                "suggested_platforms": clip.suggested_platforms,
                "viral_reasoning": clip.viral_reasoning,
                "status": clip.status
            }
            summary["clips"].append(clip_summary)
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2)
    
    def get_pending_sessions(self) -> List[Dict[str, Any]]:
        """Get all pending review sessions."""
        pending_sessions = []
        
        for session_folder in self.folders["pending"].iterdir():
            if session_folder.is_dir() and not session_folder.name.startswith('.'):
                metadata_file = session_folder / "session_metadata.json"
                if metadata_file.exists():
                    with open(metadata_file, 'r', encoding='utf-8') as f:
                        session_data = json.load(f)
                    pending_sessions.append(session_data)
        
        return sorted(pending_sessions, key=lambda x: x["session_info"]["created_at"], reverse=True)
