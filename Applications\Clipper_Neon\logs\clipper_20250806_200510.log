2025-08-06 20:05:10,843 - transcription_engine - INFO - YouTube Transcript API available for perfect timestamps
2025-08-06 20:05:12,314 - transcription_engine - INFO - Enhanced whisper-timestamped available for accurate timestamps
2025-08-06 20:05:14,363 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 20:05:14,364 - transcription_engine - INFO - YouTube Transcript API available for perfect timestamps
2025-08-06 20:05:14,364 - transcription_engine - INFO - Enhanced whisper-timestamped available for accurate timestamps
2025-08-06 20:05:16,412 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 20:05:16,413 - auto_processor - INFO - File watcher setup for: C:\AI-Hub\Applications\Clipper_Neon\data_in
2025-08-06 20:05:16,413 - clipper_main - INFO - Starting processing for: https://youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:05:16,413 - clipper_main - INFO - Step 1: Downloading video...
2025-08-06 20:05:16,413 - video_downloader - INFO - Starting download for: https://youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:05:16,413 - video_downloader - INFO - Fetching video info for: https://youtube.com/watch?v=xKa_6kzOl0M
