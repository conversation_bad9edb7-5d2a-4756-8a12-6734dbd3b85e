2025-08-06 20:05:10,843 - transcription_engine - INFO - YouTube Transcript API available for perfect timestamps
2025-08-06 20:05:12,314 - transcription_engine - INFO - Enhanced whisper-timestamped available for accurate timestamps
2025-08-06 20:05:14,363 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 20:05:14,364 - transcription_engine - INFO - YouTube Transcript API available for perfect timestamps
2025-08-06 20:05:14,364 - transcription_engine - INFO - Enhanced whisper-timestamped available for accurate timestamps
2025-08-06 20:05:16,412 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 20:05:16,413 - auto_processor - INFO - File watcher setup for: C:\AI-Hub\Applications\Clipper_Neon\data_in
2025-08-06 20:05:16,413 - clipper_main - INFO - Starting processing for: https://youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:05:16,413 - clipper_main - INFO - Step 1: Downloading video...
2025-08-06 20:05:16,413 - video_downloader - INFO - Starting download for: https://youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:05:16,413 - video_downloader - INFO - Fetching video info for: https://youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:05:22,410 - video_downloader - INFO - Video info retrieved: we broke up.. (713s)
2025-08-06 20:05:22,412 - video_downloader - INFO - Executing yt-dlp download...
2025-08-06 20:05:34,725 - video_downloader - INFO - Download completed: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 20:05:34,725 - clipper_main - INFO - Step 2: Generating transcript...
2025-08-06 20:05:34,725 - transcription_engine - INFO - Starting transcription for: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 20:05:34,725 - transcription_engine - INFO - Fetching YouTube transcript for video ID: xKa_6kzOl0M
2025-08-06 20:05:34,725 - transcription_engine - WARNING - YouTube transcript extraction failed: type object 'YouTubeTranscriptApi' has no attribute 'get_transcript'
2025-08-06 20:05:34,725 - transcription_engine - INFO - Extracting audio from video...
2025-08-06 20:05:35,110 - transcription_engine - INFO - Audio extracted: C:\Users\<USER>\AppData\Local\Temp\tmpafgphhpr.wav (21.7 MB)
2025-08-06 20:05:35,110 - transcription_engine - INFO - Loading enhanced Whisper model...
2025-08-06 20:05:35,549 - transcription_engine - INFO - Transcribing with enhanced Whisper (accurate timestamps)...
