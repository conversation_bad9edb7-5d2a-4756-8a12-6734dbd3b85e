2025-08-06 19:24:21,997 - transcription_engine - INFO - YouTube Transcript API available for perfect timestamps
2025-08-06 19:24:23,448 - transcription_engine - INFO - Enhanced whisper-timestamped available for accurate timestamps
2025-08-06 19:24:25,476 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 19:24:25,478 - transcription_engine - INFO - YouTube Transcript API available for perfect timestamps
2025-08-06 19:24:25,478 - transcription_engine - INFO - Enhanced whisper-timestamped available for accurate timestamps
2025-08-06 19:24:27,501 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 19:24:27,501 - auto_processor - INFO - File watcher setup for: C:\AI-Hub\Applications\Clipper_Neon\data_in
2025-08-06 19:24:27,502 - clipper_main - INFO - Starting processing for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 19:24:27,502 - clipper_main - INFO - Step 1: Downloading video...
2025-08-06 19:24:27,502 - video_downloader - INFO - Starting download for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 19:24:27,502 - video_downloader - INFO - Fetching video info for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 19:24:31,744 - video_downloader - INFO - Video info retrieved: we broke up.. (713s)
2025-08-06 19:24:31,745 - video_downloader - INFO - Executing yt-dlp download...
2025-08-06 19:24:38,293 - video_downloader - INFO - Download completed: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 19:24:38,293 - clipper_main - INFO - Step 2: Generating transcript...
2025-08-06 19:24:38,293 - transcription_engine - INFO - Starting transcription for: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 19:24:38,294 - transcription_engine - INFO - Fetching YouTube transcript for video ID: xKa_6kzOl0M
2025-08-06 19:24:38,294 - transcription_engine - WARNING - YouTube transcript extraction failed: type object 'YouTubeTranscriptApi' has no attribute 'get_transcript'
2025-08-06 19:24:38,294 - transcription_engine - INFO - Extracting audio from video...
2025-08-06 19:24:38,679 - transcription_engine - INFO - Audio extracted: C:\Users\<USER>\AppData\Local\Temp\tmp392k0tpy.wav (21.7 MB)
2025-08-06 19:24:38,680 - transcription_engine - INFO - Loading enhanced Whisper model...
2025-08-06 19:24:39,110 - transcription_engine - INFO - Transcribing with enhanced Whisper (accurate timestamps)...
2025-08-06 19:24:39,110 - transcription_engine - ERROR - Whisper transcription failed: transcribe_timestamped() got an unexpected keyword argument 'word_timestamps'
2025-08-06 19:24:39,110 - transcription_engine - WARNING - Using fallback transcription method
2025-08-06 19:24:39,120 - transcription_engine - WARNING - speech_recognition library not available
2025-08-06 19:24:39,120 - transcription_engine - WARNING - No transcription method available. Using placeholder.
2025-08-06 19:24:39,120 - transcription_engine - INFO - Transcription completed. Length: 76 characters
2025-08-06 19:24:39,122 - clipper_main - INFO - Step 3: Detecting highlights with AI...
2025-08-06 19:24:39,123 - highlight_detector - INFO - Detecting highlights using deepseek-r1:latest
2025-08-06 19:24:50,641 - highlight_detector - ERROR - Error parsing highlights: No JSON found in response
2025-08-06 19:24:50,641 - highlight_detector - WARNING - Using fallback highlight detection
2025-08-06 19:24:50,641 - highlight_detector - INFO - Raw highlights before filtering: 3
2025-08-06 19:24:50,644 - highlight_detector - INFO - Detected 3 valid highlights
2025-08-06 19:24:50,644 - clipper_main - INFO - Step 4: Extracting video clips...
2025-08-06 19:24:50,644 - video_clipper - INFO - Extracting clip 1: Highlight 1 (10.0s-55.0s)
2025-08-06 19:24:58,686 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\clip_01_Highlight_1.mp4 (9.9 MB)
2025-08-06 19:24:58,686 - video_clipper - INFO - Extracting clip 2: Highlight 2 (188.2s-233.2s)
2025-08-06 19:25:12,734 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\clip_02_Highlight_2.mp4 (12.6 MB)
2025-08-06 19:25:12,734 - video_clipper - INFO - Extracting clip 3: Highlight 3 (366.5s-411.5s)
2025-08-06 19:25:32,634 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\clip_03_Highlight_3.mp4 (12.7 MB)
2025-08-06 19:25:32,634 - clipper_main - INFO - Step 5: Post-processing clips...
2025-08-06 19:25:32,634 - post_processor - INFO - Post-processing 3 clips
2025-08-06 19:25:32,634 - clipper_main - ERROR - Error processing video https://www.youtube.com/watch?v=xKa_6kzOl0M: [WinError 3] The system cannot find the path specified: 'C:\\AI-Hub\\Applications\\Clipper_Neon\\data_out\\20250806_192532_we_broke_up..\\clips'
