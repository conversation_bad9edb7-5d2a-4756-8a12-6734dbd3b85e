"""
Configuration management for Clipper_Neon
=========================================

Handles all configuration settings and paths for the YouTube clipping system.
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class ClipperConfig:
    """Configuration class for Clipper_Neon system."""
    
    # Paths
    project_root: str = ""
    utilities_path: str = ""
    output_dir: str = ""
    temp_dir: str = ""
    
    # AI/LLM Settings
    ollama_host: str = "http://localhost:11434"
    default_model: str = "deepseek-r1:latest"
    highlight_detection_model: str = "deepseek-r1:latest"
    
    # Video Processing
    max_clips_per_video: int = 5
    min_clip_duration: float = 10.0  # seconds
    max_clip_duration: float = 60.0  # seconds
    video_quality: str = "720p"
    audio_quality: str = "192k"
    
    # Transcription
    transcription_model: str = "whisper"
    language: str = "auto"
    
    # Highlight Detection
    highlight_confidence_threshold: float = 0.3
    viral_keywords: list = None
    
    # Output Settings
    clip_format: str = "mp4"
    include_captions: bool = True
    watermark_enabled: bool = False
    
    # Logging
    log_level: str = "INFO"
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize configuration with defaults and load from file if provided."""
        # Set default paths
        self.project_root = str(Path(__file__).parent.parent)
        self.utilities_path = str(Path(self.project_root).parent.parent / "Core" / "Utilities")
        self.output_dir = str(Path(self.project_root) / "data_out")
        self.temp_dir = str(Path(self.project_root) / "temp")
        
        # Default viral keywords for highlight detection
        self.viral_keywords = [
            "amazing", "incredible", "unbelievable", "shocking", "viral",
            "trending", "must see", "epic", "insane", "crazy", "wow",
            "breakthrough", "exclusive", "secret", "revealed", "exposed"
        ]
        
        # Load from file if provided
        if config_path and Path(config_path).exists():
            self.load_from_file(config_path)
        
        # Create necessary directories
        self._create_directories()
    
    def load_from_file(self, config_path: str):
        """Load configuration from JSON file."""
        try:
            with open(config_path, 'r') as f:
                config_data = json.load(f)
            
            # Update attributes with loaded data
            for key, value in config_data.items():
                if hasattr(self, key):
                    setattr(self, key, value)
                    
        except Exception as e:
            print(f"Warning: Could not load config from {config_path}: {e}")
    
    def save_to_file(self, config_path: str):
        """Save current configuration to JSON file."""
        config_data = asdict(self)
        
        with open(config_path, 'w') as f:
            json.dump(config_data, f, indent=2)
    
    def _create_directories(self):
        """Create necessary directories if they don't exist."""
        directories = [
            self.output_dir,
            self.temp_dir,
            str(Path(self.project_root) / "logs"),
            str(Path(self.project_root) / "data_in"),
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    @property
    def ffmpeg_path(self) -> str:
        """Get path to ffmpeg executable."""
        return str(Path(self.utilities_path) / "ffmpeg.exe")
    
    @property
    def ytdlp_path(self) -> str:
        """Get path to yt-dlp executable."""
        return str(Path(self.utilities_path) / "yt-dlp.exe")
    
    def get_highlight_prompt(self) -> str:
        """Get the elite V.I.R.A.L. framework prompt for viral highlight detection."""
        return """
You are an ELITE viral clipper, member of a secret society of content creators who understand the neurological triggers that make content irresistibly shareable. Your mission: analyze this transcript using the V.I.R.A.L. framework to identify moments with maximum viral potential.

CRITICAL: Generate VIRAL-OPTIMIZED TITLES that are platform-ready for immediate upload. Follow this proven viral title framework:

**VIRAL TITLE FORMULA:**
- EMOTIONAL HOOKS: PSYCHO, INSANE, SHOCKING, WILD, CRAZY, UNBELIEVABLE
- SPECIFIC STAKES: Dollar amounts ($60K), consequences (SWAT, ARRESTED, FIRED)
- AUTHORITY/DRAMA: CARTEL, CEO, CELEBRITY names, EXPOSED, LEAKED
- STRATEGIC TRUNCATION: Cut at 45-50 chars to create cliffhangers
- PLATFORM READY: Use underscores, no special chars, upload-ready

**EXAMPLES OF VIRAL TITLES:**
- "PSYCHO_Assistant_STOLE_60K_Got_SWAT_Raided_Af"
- "I_Interviewed_The_CARTEL_in_Colombia_They_Tol"
- "Iggy_Azalea_Admits_Men_Lick_Her_Underarms_My_"
- "INSANE_Breakup_LIVE_She_EXPOSED_Everything_On"
- "CEO_FIRED_Me_For_This_Video_LEAKED_Internal_Em"

**TITLE REQUIREMENTS:**
1. Start with emotional hook (PSYCHO, INSANE, SHOCKING, etc.)
2. Include specific stakes/consequences
3. Add authority figures or drama elements
4. Truncate strategically at 45-50 characters for cliffhanger
5. Use underscores instead of spaces
6. NO quotation marks, colons, or special characters
7. Platform-ready for direct upload

V.I.R.A.L. FRAMEWORK:
- V (Visual Interrupt, 0-3s): Pattern interrupts, dopamine hooks, cognitive dissonance
- I (Intrigue, 3-10s): Curiosity gaps, emotional layering, promise of value
- R (Reward, 10-30s): Value delivery, surprise, humor, variable reward
- A (Amplification, 30-45s): Shareable moments that trigger comments/rewatches
- L (Last, 45-60s): Emotional close, memorable takeaway, "keep watching" effect

VIRAL SCORING CRITERIA:
- Hook Strength (1-10): How quickly it grabs attention
- Retention Power (1-10): How well it holds viewers
- Share Potential (1-10): Likelihood of being shared/commented
- Cognitive Load (1-10): Mental effort required (lower is better)
- VIRAL Score = (Hook × Retention × Share) ÷ Cognitive Load

IDENTIFY MOMENTS WITH:
1. **Pattern Interrupts**: Sudden changes, bold claims, "wait what?" moments
2. **Curiosity Gaps**: Questions, counterintuitive statements, setup-payoff loops
3. **Emotional Triggers**: Surprise, humor, shock, inspiration, controversy
4. **Rewatch Value**: Callbacks, hidden details, layered meaning
5. **Social Currency**: Moments people want to share to look smart/funny/informed

AVOID:
- Slow builds without payoff
- Complex explanations
- Filler content
- Low-energy segments

Transcript:
{transcript}

Video Metadata:
Title: {title}
Duration: {duration} seconds
Description: {description}

Respond in JSON format with TOP {max_clips} viral moments:
{{
  "highlights": [
    {{
      "start_time": 45.2,
      "end_time": 78.5,
      "title": "INSANE_Breakup_LIVE_She_EXPOSED_Everything_On",
      "description": "Why this hooks viewers instantly",
      "viral_components": {{
        "visual_interrupt": "Sudden topic change creates cognitive dissonance",
        "intrigue": "Sets up curiosity gap about unexpected revelation",
        "reward": "Delivers surprising insight with humor",
        "amplification": "Quotable moment that triggers comments",
        "lasting": "Ends with memorable takeaway"
      }},
      "viral_scores": {{
        "hook_strength": 9,
        "retention_power": 8,
        "share_potential": 9,
        "cognitive_load": 3,
        "viral_score": 216
      }},
      "suggested_platforms": ["tiktok", "youtube_shorts", "instagram"],
      "viral_reasoning": "Perfect pattern interrupt + curiosity gap + quotable moment",
      "keywords": ["shocking", "viral", "unexpected"]
    }}
  ]
}}
"""
    
    def validate(self) -> bool:
        """Validate configuration settings."""
        errors = []
        
        # Check required paths exist
        if not Path(self.ffmpeg_path).exists():
            errors.append(f"ffmpeg not found at: {self.ffmpeg_path}")
        
        if not Path(self.ytdlp_path).exists():
            errors.append(f"yt-dlp not found at: {self.ytdlp_path}")
        
        # Check numeric ranges
        if self.max_clips_per_video < 1 or self.max_clips_per_video > 20:
            errors.append("max_clips_per_video must be between 1 and 20")
        
        if self.min_clip_duration >= self.max_clip_duration:
            errors.append("min_clip_duration must be less than max_clip_duration")
        
        if self.highlight_confidence_threshold < 0.0 or self.highlight_confidence_threshold > 1.0:
            errors.append("highlight_confidence_threshold must be between 0.0 and 1.0")
        
        if errors:
            print("Configuration validation errors:")
            for error in errors:
                print(f"  - {error}")
            return False
        
        return True


def create_default_config_file(output_path: str):
    """Create a default configuration file."""
    config = ClipperConfig()
    config.save_to_file(output_path)
    print(f"Default configuration saved to: {output_path}")


if __name__ == "__main__":
    # Create default config file for reference
    config_path = Path(__file__).parent.parent / "configs" / "default_config.json"
    create_default_config_file(str(config_path))
