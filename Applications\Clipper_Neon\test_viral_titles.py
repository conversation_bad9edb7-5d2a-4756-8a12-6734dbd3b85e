#!/usr/bin/env python3
"""
Test script to verify viral title generation is working correctly.
"""

import sys
import os
sys.path.append('src')

from highlight_detector import Highlight
from post_processor import PostProcessor
from config import ClipperConfig

def test_viral_title_generation():
    """Test the viral title generation system."""
    print("🧪 Testing Viral Title Generation System...")
    
    # Create test highlight
    test_highlight = Highlight(
        start_time=45.2,
        end_time=78.5,
        title="Unexpected Breakup Announcement",
        description="Strong emotional moment with pattern interrupt",
        confidence=0.85,
        keywords=["breakup", "emotional", "shocking"],
        viral_components={
            "visual_interrupt": "Sudden announcement creates cognitive dissonance",
            "intrigue": "Sets up curiosity gap about relationship drama",
            "amplification": "Quotable moment that triggers comments"
        },
        viral_scores={
            "hook_strength": 8,
            "retention_power": 7,
            "share_potential": 9,
            "cognitive_load": 3,
            "viral_score": 168
        },
        suggested_platforms=["tiktok", "youtube_shorts", "instagram"],
        viral_reasoning="Perfect emotional trigger + curiosity gap + shareable moment"
    )
    
    # Test metadata
    test_metadata = {
        'title': 'we broke up',
        'webpage_url': 'https://www.youtube.com/watch?v=xKa_6kzOl0M',
        'duration': 660
    }
    
    # Create config and post processor
    config = ClipperConfig()
    post_processor = PostProcessor(config)
    
    # Test viral filename creation
    print("\n📝 Testing Viral Filename Creation:")
    viral_filename = post_processor._create_viral_filename("INSANE_Breakup_LIVE_She_EXPOSED_Everything_On", 0)
    print(f"Generated filename: {viral_filename}")
    
    # Test enhanced title generation (if Ollama is available)
    print("\n🤖 Testing AI Title Enhancement:")
    try:
        enhanced_title = post_processor._generate_enhanced_title(test_highlight, test_metadata)
        print(f"Original title: {test_highlight.title}")
        print(f"Enhanced title: {enhanced_title}")
        
        # Test viral filename with enhanced title
        viral_filename_enhanced = post_processor._create_viral_filename(enhanced_title, 0)
        print(f"Viral filename: {viral_filename_enhanced}")
        
    except Exception as e:
        print(f"AI enhancement not available: {e}")
        print("This is normal if Ollama/DeepSeek-R1 is not running")
    
    print("\n✅ Viral Title System Test Complete!")
    print("\nExpected output format:")
    print("- Filenames: 01_VIRAL_TITLE_WITH_UNDERSCORES.mp4")
    print("- Titles: EMOTIONAL_HOOK + STAKES + DRAMA + TRUNCATION")
    print("- Platform-ready for direct upload")

if __name__ == "__main__":
    test_viral_title_generation()
