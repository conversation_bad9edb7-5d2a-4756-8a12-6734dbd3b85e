2025-08-06 19:44:57,606 - transcription_engine - INFO - YouTube Transcript API available for perfect timestamps
2025-08-06 19:44:59,151 - transcription_engine - INFO - Enhanced whisper-timestamped available for accurate timestamps
2025-08-06 19:45:01,200 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 19:45:01,201 - transcription_engine - INFO - YouTube Transcript API available for perfect timestamps
2025-08-06 19:45:01,201 - transcription_engine - INFO - Enhanced whisper-timestamped available for accurate timestamps
2025-08-06 19:45:03,235 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 19:45:03,235 - auto_processor - INFO - File watcher setup for: C:\AI-Hub\Applications\Clipper_Neon\data_in
2025-08-06 19:45:03,236 - clipper_main - INFO - Starting processing for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 19:45:03,236 - clipper_main - INFO - Step 1: Downloading video...
2025-08-06 19:45:03,236 - video_downloader - INFO - Starting download for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 19:45:03,236 - video_downloader - INFO - Fetching video info for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 19:45:07,320 - video_downloader - INFO - Video info retrieved: we broke up.. (713s)
2025-08-06 19:45:07,320 - video_downloader - INFO - Executing yt-dlp download...
2025-08-06 19:45:11,915 - video_downloader - INFO - Download completed: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 19:45:11,916 - clipper_main - INFO - Step 2: Generating transcript...
2025-08-06 19:45:11,916 - transcription_engine - INFO - Starting transcription for: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 19:45:11,916 - transcription_engine - INFO - Fetching YouTube transcript for video ID: xKa_6kzOl0M
2025-08-06 19:45:11,916 - transcription_engine - WARNING - YouTube transcript extraction failed: type object 'YouTubeTranscriptApi' has no attribute 'get_transcript'
2025-08-06 19:45:11,916 - transcription_engine - INFO - Extracting audio from video...
2025-08-06 19:45:12,306 - transcription_engine - INFO - Audio extracted: C:\Users\<USER>\AppData\Local\Temp\tmpvyoaxnek.wav (21.7 MB)
2025-08-06 19:45:12,306 - transcription_engine - INFO - Loading enhanced Whisper model...
2025-08-06 19:45:12,749 - transcription_engine - INFO - Transcribing with enhanced Whisper (accurate timestamps)...
2025-08-06 19:47:17,491 - transcription_engine - INFO - Enhanced Whisper transcription completed. Language: en
2025-08-06 19:47:17,492 - transcription_engine - INFO - Transcription completed. Length: 13122 characters
2025-08-06 19:47:17,494 - clipper_main - INFO - Step 3: Detecting highlights with AI...
2025-08-06 19:47:17,495 - highlight_detector - INFO - Detecting highlights using deepseek-r1:latest
2025-08-06 19:47:36,359 - highlight_detector - INFO - Parsed 1 highlights from AI response
2025-08-06 19:47:36,359 - highlight_detector - INFO - Raw highlights before filtering: 1
2025-08-06 19:47:36,362 - highlight_detector - INFO - Detected 1 valid highlights
2025-08-06 19:47:36,362 - clipper_main - INFO - Step 4: Extracting video clips...
2025-08-06 19:47:36,362 - video_clipper - INFO - Extracting clip 1: Unexpected Breakup Announcement (45.2s-78.5s)
2025-08-06 19:47:42,632 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\clip_01_Unexpected_Breakup_Announcement.mp4 (6.1 MB)
2025-08-06 19:47:42,633 - clipper_main - INFO - Step 5: Post-processing clips...
2025-08-06 19:47:42,633 - post_processor - INFO - Post-processing 1 clips
2025-08-06 19:47:42,633 - clipper_main - ERROR - Error processing video https://www.youtube.com/watch?v=xKa_6kzOl0M: [WinError 3] The system cannot find the path specified: 'C:\\AI-Hub\\Applications\\Clipper_Neon\\data_out\\20250806_194742_we_broke_up..\\clips'
