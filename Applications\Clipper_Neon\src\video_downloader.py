"""
Video Downloader Module for Clipper_Neon
========================================

Handles YouTube video downloading using yt-dlp utility.
"""

import os
import json
import subprocess
import logging
from pathlib import Path
from typing import Dict, Tuple, Optional
from urllib.parse import urlparse, parse_qs


class VideoDownloader:
    """Handles YouTube video downloading and metadata extraction."""
    
    def __init__(self, config):
        """Initialize the video downloader."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Verify yt-dlp is available
        if not Path(self.config.ytdlp_path).exists():
            raise FileNotFoundError(f"yt-dlp not found at: {self.config.ytdlp_path}")
    
    def extract_video_id(self, youtube_url: str) -> str:
        """Extract video ID from YouTube URL."""
        parsed_url = urlparse(youtube_url)
        
        if parsed_url.hostname in ['youtu.be']:
            return parsed_url.path[1:]
        elif parsed_url.hostname in ['www.youtube.com', 'youtube.com']:
            if parsed_url.path == '/watch':
                return parse_qs(parsed_url.query)['v'][0]
            elif parsed_url.path.startswith('/embed/'):
                return parsed_url.path.split('/')[2]
            elif parsed_url.path.startswith('/v/'):
                return parsed_url.path.split('/')[2]
        
        raise ValueError(f"Could not extract video ID from URL: {youtube_url}")
    
    def get_video_info(self, youtube_url: str) -> Dict:
        """Get video metadata without downloading."""
        self.logger.info(f"Fetching video info for: {youtube_url}")
        
        cmd = [
            self.config.ytdlp_path,
            "--dump-json",
            "--no-download",
            youtube_url
        ]
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True,
                cwd=self.config.temp_dir
            )
            
            metadata = json.loads(result.stdout)
            
            # Extract relevant information
            info = {
                'id': metadata.get('id', ''),
                'title': metadata.get('title', ''),
                'description': metadata.get('description', ''),
                'duration': metadata.get('duration', 0),
                'uploader': metadata.get('uploader', ''),
                'upload_date': metadata.get('upload_date', ''),
                'view_count': metadata.get('view_count', 0),
                'like_count': metadata.get('like_count', 0),
                'thumbnail': metadata.get('thumbnail', ''),
                'webpage_url': metadata.get('webpage_url', youtube_url),
                'categories': metadata.get('categories', []),
                'tags': metadata.get('tags', [])
            }
            
            self.logger.info(f"Video info retrieved: {info['title']} ({info['duration']}s)")
            return info
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Failed to get video info: {e.stderr}")
            raise
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse video metadata: {e}")
            raise
    
    def download(self, youtube_url: str, output_dir: Optional[str] = None) -> Tuple[str, Dict]:
        """
        Download YouTube video and return path and metadata.
        
        Args:
            youtube_url: YouTube video URL
            output_dir: Optional output directory (uses temp_dir if not specified)
            
        Returns:
            Tuple of (video_file_path, metadata_dict)
        """
        self.logger.info(f"Starting download for: {youtube_url}")
        
        # Get video metadata first
        metadata = self.get_video_info(youtube_url)
        video_id = metadata['id']
        
        # Set output directory
        if output_dir is None:
            output_dir = self.config.temp_dir
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Define output filename template
        output_template = str(output_path / f"{video_id}.%(ext)s")
        
        # Build yt-dlp command with highest quality settings
        cmd = [
            self.config.ytdlp_path,
            "--format", self._get_format_selector(),
            "--output", output_template,
            "--merge-output-format", "mp4",  # Ensure final output is mp4
            "--write-info-json",
            "--write-thumbnail",
            youtube_url
        ]
        
        try:
            self.logger.info("Executing yt-dlp download...")
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True,
                cwd=output_dir
            )
            
            # Find the downloaded video file
            video_file = self._find_downloaded_file(output_path, video_id)
            
            if not video_file:
                raise FileNotFoundError("Downloaded video file not found")
            
            self.logger.info(f"Download completed: {video_file}")
            return str(video_file), metadata
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Download failed: {e.stderr}")
            raise
    
    def _get_format_selector(self) -> str:
        """Get format selector for highest quality downloads."""
        # Always fetch the best quality available (up to 4K if available)
        # This prioritizes separate video+audio streams for best quality,
        # then falls back to best single file
        return "bestvideo[ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4]/best"
    
    def _find_downloaded_file(self, output_dir: Path, video_id: str) -> Optional[Path]:
        """Find the downloaded video file."""
        # Common video extensions
        extensions = ['.mp4', '.mkv', '.webm', '.avi', '.mov']
        
        for ext in extensions:
            video_file = output_dir / f"{video_id}{ext}"
            if video_file.exists():
                return video_file
        
        # If exact match not found, look for files starting with video_id
        for file_path in output_dir.glob(f"{video_id}.*"):
            if file_path.suffix.lower() in extensions:
                return file_path
        
        return None
    
    def cleanup_temp_files(self, video_id: str, keep_video: bool = True):
        """Clean up temporary files after processing."""
        temp_dir = Path(self.config.temp_dir)
        
        patterns_to_remove = [
            f"{video_id}.info.json",
            f"{video_id}.webp",
            f"{video_id}.jpg",
            f"{video_id}.png"
        ]
        
        if not keep_video:
            patterns_to_remove.extend([
                f"{video_id}.mp4",
                f"{video_id}.mkv", 
                f"{video_id}.webm"
            ])
        
        for pattern in patterns_to_remove:
            for file_path in temp_dir.glob(pattern):
                try:
                    file_path.unlink()
                    self.logger.debug(f"Removed temp file: {file_path}")
                except Exception as e:
                    self.logger.warning(f"Could not remove {file_path}: {e}")
    
    def validate_url(self, url: str) -> bool:
        """Validate if URL is a valid YouTube URL."""
        try:
            self.extract_video_id(url)
            return True
        except ValueError:
            return False


if __name__ == "__main__":
    # Test the downloader
    from config import ClipperConfig
    
    config = ClipperConfig()
    downloader = VideoDownloader(config)
    
    # Test URL validation
    test_urls = [
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
        "https://youtu.be/dQw4w9WgXcQ",
        "invalid_url"
    ]
    
    for url in test_urls:
        print(f"URL: {url} - Valid: {downloader.validate_url(url)}")
